<template>
  <div class="setting-group-sidebar flex h-full flex-col">
    <!-- Search and Action Buttons -->
    <div class="flex items-center gap-3">
      <el-input
        v-model="searchQuery"
        placeholder="Tìm kiếm nhóm..."
        clearable
        class="search-input flex-1"
        @input="handleInput"
        @clear="handleClear"
      >
        <template #prefix>
          <el-icon><Search /></el-icon>
        </template>
      </el-input>

      <!-- Action buttons group -->
      <div class="flex gap-1.5">
        <ButtonCommon text="Tạo nhóm" :icon="PlusIcon" type="primary" size="medium" @click="$emit('create-group')" />
      </div>
    </div>

    <!-- Search Results Info -->
    <div v-if="searchQuery && !loading" class="mt-2 text-base text-gray-500 dark:text-gray-400">
      Tìm thấy {{ filteredGroups.length }} nhóm{{ filteredGroups.length !== 1 ? '' : '' }}
    </div>

    <!-- Groups List with fixed height -->
    <div
      class="group-list-scroll mt-5 flex-1 overflow-hidden rounded-lg border border-gray-200 bg-gray-50 dark:border-gray-700 dark:bg-gray-900/50"
    >
      <div v-loading="loading" class="h-full overflow-y-auto p-2" @scroll="handleScroll" ref="scrollContainer">
        <div
          v-for="group in filteredGroups"
          :key="group.id"
          class="mb-2 flex cursor-pointer items-center justify-between rounded-lg border p-3 transition-all duration-200 hover:shadow-md dark:hover:shadow-lg"
          :class="{
            'border-blue-400 bg-gradient-to-r from-blue-50 to-indigo-50 shadow-lg dark:border-blue-500 dark:from-blue-900/30 dark:to-indigo-900/30 dark:shadow-blue-500/20':
              selectedGroup?.id === group.id,
            'border-gray-200 bg-white hover:border-gray-300 dark:border-gray-600 dark:bg-gray-800 dark:hover:border-gray-500':
              selectedGroup?.id !== group.id,
          }"
          @click="$emit('select-group', group)"
        >
          <!-- Group Info -->
          <div class="flex-1">
            <div class="flex items-center gap-2">
              <div class="text-base font-medium text-gray-900 dark:text-white">
                {{ group.name || 'Không xác định' }}
              </div>
              <!-- Selected indicator -->
              <div v-if="selectedGroup?.id === group.id" class="flex items-center gap-1">
                <div class="h-2 w-2 animate-pulse rounded-full bg-blue-500"></div>
                <span class="text-sm font-medium text-blue-600 dark:text-blue-400"> Đang chọn </span>
              </div>
            </div>
            <div class="text-sm text-gray-500 dark:text-gray-400">
              {{ group.group_key }}
            </div>
            <div v-if="group.description" class="mt-1 line-clamp-2 text-sm text-gray-400 dark:text-gray-500">
              {{ group.description }}
            </div>
          </div>

          <!-- Action Buttons -->
          <div class="flex items-center gap-2">
            <ButtonCommon
              :icon="EditIcon"
              type="info"
              size="small"
              rounded
              tooltip="Chỉnh sửa nhóm"
              @click.stop="$emit('edit-group', group)"
            />
            <el-tooltip
              :content="isGroupProtected(group) ? getGroupTooltip(group) : 'Xóa nhóm'"
              placement="top"
            >
              <ButtonCommon
                :icon="TrashIcon"
                type="danger"
                size="small"
                rounded
                :disabled="isGroupProtected(group)"
                @click.stop="$emit('delete-group', group.id)"
              />
            </el-tooltip>
          </div>
        </div>

        <!-- Loading more indicator -->
        <div v-if="loadingMore" class="py-4 text-center">
          <el-icon class="animate-spin text-blue-500">
            <Loading />
          </el-icon>
          <div class="mt-2 text-sm text-gray-500 dark:text-gray-400">Đang tải thêm...</div>
        </div>

        <!-- Empty state -->
        <div v-if="!loading && filteredGroups.length === 0" class="py-8 text-center text-gray-500 dark:text-gray-400">
          <div class="flex flex-col items-center gap-2">
            <div class="text-lg">🔍</div>
            <div class="text-lg font-medium">
              {{ searchQuery ? 'Không tìm thấy nhóm nào' : 'Không có nhóm nào' }}
            </div>
            <div class="text-base">
              {{ searchQuery ? `Không có nhóm nào phù hợp với "${searchQuery}"` : 'Hãy tạo nhóm đầu tiên' }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { Search, Loading } from '@element-plus/icons-vue'
import { PlusIcon, EditIcon, TrashIcon } from '@/components/icons/index.js'
import ButtonCommon from '@/components/common/ButtonCommon.vue'
import {
  isProtectedSettingGroup,
  getProtectedGroupTooltipMessage
} from '@/utils/configs/protected-settings.config.js'

// Props
const props = defineProps({
  groups: {
    type: Array,
    default: () => [],
  },
  selectedGroup: {
    type: Object,
    default: null,
  },
  loading: {
    type: Boolean,
    default: false,
  },
  loadingMore: {
    type: Boolean,
    default: false,
  },
  hasMorePages: {
    type: Boolean,
    default: false,
  },
})

// Emits
const emit = defineEmits(['create-group', 'edit-group', 'delete-group', 'select-group', 'refresh', 'load-more'])

// Local state
const searchQuery = ref('')
const scrollContainer = ref(null)

// Computed
const filteredGroups = computed(() => {
  if (!searchQuery.value) return props.groups || []

  return (props.groups || []).filter(
    (group) =>
      group.name?.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
      group.group_key?.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
      group.description?.toLowerCase().includes(searchQuery.value.toLowerCase()),
  )
})

// Methods
const handleInput = () => {
  // Search is reactive through computed property
}

const handleClear = () => {
  searchQuery.value = ''
}

const handleScroll = (event) => {
  if (props.loadingMore || !props.hasMorePages || searchQuery.value) return

  const { scrollTop, scrollHeight, clientHeight } = event.target
  const threshold = 50 // pixels from bottom

  if (scrollHeight - scrollTop - clientHeight < threshold) {
    emit('load-more')
  }
}

// Protected group methods
const isGroupProtected = (group) => {
  return isProtectedSettingGroup(group.group_key, group.name)
}

const getGroupTooltip = (group) => {
  return getProtectedGroupTooltipMessage(group.group_key)
}
</script>

<style scoped>
/* Match input height with button */
.search-input :deep(.el-input__wrapper) {
  height: 38px;
  display: flex;
  align-items: center;
}

.search-input :deep(.el-input__inner) {
  height: 100%;
}

/* List container */
.group-list-scroll {
  height: calc(100% - 60px); /* Adjust based on search bar height */
  max-height: 580px;
  min-height: 200px; /* Ensure minimum height */
}

.group-list-scroll :deep(.el-loading-mask) {
  border-radius: 0.5rem;
}

/* Custom scrollbar styling */
.group-list-scroll :deep(div:first-child) {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e1 transparent;
}

.group-list-scroll :deep(div:first-child)::-webkit-scrollbar {
  width: 6px;
}

.group-list-scroll :deep(div:first-child)::-webkit-scrollbar-track {
  background: transparent;
}

.group-list-scroll :deep(div:first-child)::-webkit-scrollbar-thumb {
  background-color: #cbd5e1;
  border-radius: 3px;
  transition: background-color 0.2s;
}

.group-list-scroll :deep(div:first-child)::-webkit-scrollbar-thumb:hover {
  background-color: #94a3b8;
}

/* Dark mode scrollbar */
.dark .group-list-scroll :deep(div:first-child) {
  scrollbar-color: #475569 transparent;
}

.dark .group-list-scroll :deep(div:first-child)::-webkit-scrollbar-thumb {
  background-color: #475569;
}

.dark .group-list-scroll :deep(div:first-child)::-webkit-scrollbar-thumb:hover {
  background-color: #64748b;
}

/* Responsive */
@media (max-width: 1024px) {
  .group-list-scroll {
    height: auto;
    max-height: 400px;
  }
}

/* Utility class for text truncation */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
