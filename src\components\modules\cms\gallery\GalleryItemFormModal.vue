<template>
  <!-- Modal chỉnh sửa/thêm mới ảnh -->
  <el-dialog
    :model-value="isVisible"
    :title="formData.id ? 'Chỉnh sửa ảnh' : 'Thêm mới ảnh'"
    width="1000px"
    @close="close"
  >
    <div class="p-6">
      <!-- Form chỉnh sửa/thêm mới ảnh -->
      <el-form ref="formRef" :model="formData" label-position="top">
        <el-row :gutter="20">
          <el-col :span="12">
            <!-- Tiêu đề ảnh -->
            <el-form-item
              label="Tiêu đề"
              prop="title"
              :rules="[{ required: true, message: 'Tiêu đề là bắt buộc', trigger: 'blur' }]"
            >
              <el-input v-model="formData.title" placeholder="Nhập tiêu đề ảnh" />
            </el-form-item>

            <!-- <PERSON><PERSON> tả ảnh (alt_text) -->
            <el-form-item
              style="margin-top: 26px"
              label="Mô tả ảnh (alt_text)"
              prop="alt_text"
              :rules="[{ required: true, message: '<PERSON><PERSON> tả là bắt buộc', trigger: 'blur' }]"
            >
              <el-input v-model="formData.alt_text" placeholder="Nhập mô tả ảnh (SEO alt)" />
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <!-- Chọn ảnh -->
            <el-form-item
              label="Chọn ảnh"
              prop="image_url"
              :rules="[{ required: true, message: 'Ảnh là bắt buộc', trigger: 'blur' }]"
            >
              <div class="w-full">
                <div class="upload-area" @click="triggerFileInput">
                  <div v-if="imagePreview" class="image-preview-container">
                    <img :src="imagePreview" alt="Preview" class="image-preview" />

                    <!-- Nút xoá: chỉ hiển thị khi có ảnh -->
                    <el-button
                      v-if="imagePreview"
                      type="danger"
                      size="small"
                      @click.stop="removeImage"
                      circle
                      class="delete-btn"
                    >
                      <TrashIcon class="size-5" />
                    </el-button>
                  </div>

                  <div v-else class="no-preview">
                    <el-icon><Picture /></el-icon>
                    <span class="preview-text">Chưa có ảnh preview</span>
                    <span class="preview-subtext">
                      Upload tối đa <strong>10MB</strong>, hỗ trợ các định dạng jpg, jpeg, png, gif, webp.
                    </span>
                  </div>
                </div>

                <!-- input -->
                <input
                  ref="fileInputRef"
                  type="file"
                  accept="image/*"
                  class="hidden-file-input"
                  @change="handleFileChange"
                />
              </div>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- Đường dẫn và target -->
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="Đường dẫn (Link)" prop="link" style="margin-top: 26px">
              <el-input v-model="formData.link" placeholder="https://example.com" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="Target" prop="target" style="margin-top: 26px">
              <el-select v-model="formData.target" class="w-full">
                <el-option label="Mở ở tab hiện tại" value="_self" />
                <el-option label="Mở ở tab mới" value="_blank" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- Thứ tự hiển thị và trạng thái -->
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item
              label="Thứ tự hiển thị (order)"
              prop="order"
              :rules="[{ required: true, message: 'Bắt buộc', trigger: 'blur' }]"
              style="margin-top: 26px"
            >
              <el-input-number v-model="formData.order" :min="1" class="w-full" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="Trạng thái" prop="status" style="margin-top: 26px">
              <el-select v-model="formData.status" class="w-full">
                <el-option label="Hoạt động" :value="1" />
                <el-option label="Không hoạt động" :value="0" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <!-- Footer modal -->

    <ButtonModalCommon
      :loading="loading"
      :can-submit="true"
      cancel-text="Hủy"
      :submit-text="formData.id ? 'Cập nhật' : 'Thêm mới'"
      @cancel="close"
      @submit="submitForm"
    />
  </el-dialog>
</template>

<script setup>
import { ref, watch } from 'vue'
import {
  ElDialog,
  ElForm,
  ElFormItem,
  ElInput,
  ElButton,
  ElSelect,
  ElOption,
  ElRow,
  ElCol,
  ElInputNumber,
  ElIcon,
} from 'element-plus'
import { Picture } from '@element-plus/icons-vue'
import ButtonModalCommon from '~/components/common/ButtonModalCommon.vue'
import { TrashIcon } from '@/components/icons/index.js'

// ===== PROPS & EMITS =====

/**
 * Props của component
 */
const props = defineProps({
  /** @type {Boolean} Điều khiển hiển thị modal */
  isVisible: Boolean,

  /** @type {Object|null} Dữ liệu ảnh (null: thêm mới, object: chỉnh sửa) */
  itemData: Object,
})

/**
 * Events được emit từ component
 */
const emit = defineEmits(['close', 'save-item'])

// ===== REACTIVE STATE =====

/** @type {import('vue').Ref<Object|null>} Tham chiếu đến form để validate */
const formRef = ref(null)

/** @type {import('vue').Ref<HTMLInputElement|null>} Tham chiếu đến input file */
const fileInputRef = ref(null)

/** @type {import('vue').Ref<string>} URL preview ảnh */
const imagePreview = ref('')

/**
 * Dữ liệu form với giá trị mặc định
 * @type {import('vue').Ref<Object>}
 */
const formData = ref({
  id: null, // ID ảnh (null nếu thêm mới)
  image_url: '', // File ảnh hoặc URL ảnh
  alt_text: '', // Mô tả ảnh (SEO alt text)
  title: '', // Tiêu đề ảnh
  link: '', // Đường dẫn khi click vào ảnh
  target: '_self', // Target mở link (_self hoặc _blank)
  status: 1, // Trạng thái (1: hoạt động, 0: không hoạt động)
  order: 1, // Thứ tự hiển thị
})

// ===== WATCHERS =====

/**
 * Theo dõi thay đổi của prop itemData để cập nhật form
 * - Nếu có dữ liệu: mode chỉnh sửa
 * - Nếu null: mode thêm mới
 */
watch(
  () => props.itemData,
  (newItem) => {
    if (newItem) {
      // Chỉnh sửa: Copy dữ liệu từ prop vào form
      formData.value = { ...newItem }

      // Cập nhật preview nếu có image_url
      if (newItem.image_url) {
        imagePreview.value = newItem.image_url
      } else {
        imagePreview.value = ''
      }
    } else {
      // Thêm mới: Reset về giá trị mặc định
      formData.value = {
        id: null,
        image_url: '',
        alt_text: '',
        title: '',
        link: '',
        target: '_self',
        status: 1,
        order: 1,
      }
      imagePreview.value = ''
    }

    // Xóa các validation errors
    formRef.value?.clearValidate()
  },
  { immediate: true, deep: true },
)

// ===== METHODS =====

/**
 * Kích hoạt input file khi nhấn nút chọn ảnh
 */
const triggerFileInput = () => {
  fileInputRef.value?.click()
}

/**
 * Xử lý khi người dùng chọn file ảnh
 * - Đọc file và tạo URL preview
 * - Cập nhật image_url với File object
 * @param {Event} event - Sự kiện change của input file
 */
const handleFileChange = (event) => {
  const file = event.target.files[0]
  if (!file) return

  // Kiểm tra file có phải là ảnh không
  if (!file.type.startsWith('image/')) {
    // Có thể thêm thông báo lỗi ở đây
    return
  }

  // Lưu file object vào formData
  formData.value.image_url = file

  // Tạo URL preview
  imagePreview.value = URL.createObjectURL(file)

  // Reset input để có thể chọn lại cùng một file
  event.target.value = ''
}

const removeImage = () => {
  imagePreview.value = ''
  formData.value.image_url = ''
  if (fileInputRef.value) {
    fileInputRef.value.value = ''
  }
}

/**
 * Đóng modal và thông báo cho component cha
 */
const close = () => {
  emit('close')
}

/**
 * Xử lý submit form
 * - Validate trước khi gửi dữ liệu
 * - Emit sự kiện save-item với dữ liệu đã validate
 */
const submitForm = async () => {
  if (!formRef.value) return

  await formRef.value.validate((valid) => {
    if (valid) {
      emit('save-item', formData.value)
    }
  })
}
</script>

<style scoped>
/* Kiểu dáng tùy chỉnh */
.w-full {
  width: 100%;
}

.hidden-file-input {
  display: none;
}

.upload-area {
  cursor: pointer;
  width: 100%;
  position: relative;
}

/* Khi chưa có ảnh */
.no-preview {
  width: 100%;
  height: 150px;
  border: 1px dashed #dcdfe6;
  border-radius: 8px;
  background-color: #fafafa;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #909399;
  text-align: center;
  padding: 8px;
}

.no-preview .el-icon {
  font-size: 28px;
  margin-bottom: 6px;
  color: #c0c4cc;
}

.preview-text {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 4px;
}

.preview-subtext {
  font-size: 14px;
  color: #a8abb2;
  line-height: 1.4;
}

/* Khung preview ảnh */
.image-preview-container {
  width: 200px;
  height: 200px;
  border: 1px dashed #dcdfe6;
  border-radius: 8px;
  overflow: hidden;
  position: relative;
  background-color: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
}

.image-preview {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

/* Hover */
.upload-area:hover .image-preview-container,
.upload-area:hover .no-preview {
  border-color: #409eff;
  background-color: #f0f9ff;
}

/* Nút xóa */
.delete-btn {
  position: absolute;
  top: 6px;
  right: 6px;
  width: 32px !important;
  height: 32px !important;
  border-radius: 50% !important;
  padding: 0 !important;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2;
}

/* Footer Button Styles */
.footer-btn {
  min-width: 80px;
  font-weight: 500;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.cancel-btn {
  background: #f5f5f5 !important;
  border: 1px solid #d9d9d9 !important;
  color: #666 !important;
}

.cancel-btn:hover {
  background: #e6e6e6 !important;
  border-color: #bfbfbf !important;
  color: #333 !important;
}

.submit-btn {
  background: #1890ff !important;
  border: 1px solid #1890ff !important;
  color: #fff !important;
}

.submit-btn:hover {
  background: #40a9ff !important;
  border-color: #40a9ff !important;
}
</style>
