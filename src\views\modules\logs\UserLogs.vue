<template>
  <section class="flex h-screen flex-col">
    <PageBreadcrumb :page-title="currentPageTitle" :breadcrumbs="[{ label: 'Trang chủ', to: '/' }]" />

    <div
      class="mb-4 flex flex-1 flex-col overflow-hidden rounded-lg border border-gray-200 bg-white p-5 lg:p-6 dark:border-gray-600 dark:bg-gray-700/50"
    >
      <!-- Header -->
      <div class="mb-4 flex-shrink-0">
        <!-- Form Filter -->
        <el-input
          placeholder="Nhập từ khóa bạn muốn tìm kiếm .."
          clearable
          class="search-input w-full max-w-[450px]"
          v-model="filters.search"
          @input="handleSearch"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>

      <!-- Content -->

      <div class="min-h-0 flex-1 rounded-lg border border-gray-200 bg-white dark:border-gray-700 dark:bg-gray-800">
        <el-table
          :data="userLogs"
          height="100%"
          class="dark-table h-full"
          v-loading="loading"
          :header-cell-style="getTableHeaderStyle"
          @sort-change="handleSortChange"
        >
          <el-table-column prop="id" label="Id" width="100" align="center" :sortable="'custom'" />
          <el-table-column label="Người dùng" align="center" min-width="150" max-width="200">
            <template #default="{ row }">
              <div class="flex items-center justify-center px-3 py-4">
                <span class="line-clamp-1 text-center font-medium text-gray-900 dark:text-white" :title="row.title">
                  {{ row.actor?.name ?? 'N/A' }}
                </span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="action" label="Hành động" align="center" min-width="120" />
          <el-table-column label="Thời gian Hành động" align="center" min-width="180" max-width="250">
            <template #default="{ row }">
              <span>{{ row.action_time }}</span></template
            >
          </el-table-column>
          <el-table-column label="Model type" align="center" min-width="120" max-width="160">
            <template #default="{ row }">
              <span>{{ row.model_type ?? 'N/A' }}</span></template
            >
          </el-table-column>
          <el-table-column prop="description" label="Mô tả" align="start" min-width="300" />
          <el-table-column label="Thao tác" align="center" width="150">
            <template #default="{ row }">
              <ActionButtons :show-delete="false" :show-edit="false" @view="handleViewDetail(row.id)" />
            </template>
          </el-table-column>
        </el-table>
      </div>
      <!-- Footer -->
      <div class="mt-4 flex-shrink-0">
        <Pagination :pagination="pagination" @page-change="handlePageChange" @per-page-change="handlePerPageChange" />
      </div>
    </div>

    <!-- Model detail -->
    <Modal
      :title="`Chi tiết lịch sử hoạt động Id ${selectedLog?.id || ''}`"
      v-model="showViewDetail"
      class="mw-full max-w-[700px]"
      @close="handleClose"
    >
      <template #body>
        <div class="space-y-4 p-6">
          <h3 class="text-xl font-semibold">Thông tin</h3>

          <el-descriptions
            class="log-detail [&_.el-descriptions__cell]:!border-[#ebeef5] [&_.el-descriptions__label]:!bg-[#f5f7fa] [&_.el-descriptions__label]:!font-medium [&_.el-descriptions__label]:!text-[#606266]"
            :column="1"
            border
            label-width="200px"
          >
            <el-descriptions-item label="Người dùng">
              {{ selectedLog.actor?.name ?? 'N/A' }}
            </el-descriptions-item>

            <el-descriptions-item label="Thời gian hành động">
              {{ selectedLog.action_time ?? 'N/A' }}
            </el-descriptions-item>

            <el-descriptions-item label="Model type">
              {{ selectedLog.model_type ?? 'N/A' }}
            </el-descriptions-item>

            <el-descriptions-item label="IP">
              {{ selectedLog.ip ?? 'N/A' }}
            </el-descriptions-item>

            <el-descriptions-item label="User agent">
              <span class="break-all">{{ selectedLog.user_agent ?? 'N/A' }}</span>
            </el-descriptions-item>

            <el-descriptions-item label="Mô tả">
              <span class="break-all">{{ selectedLog.description ?? 'N/A' }}</span>
            </el-descriptions-item>
          </el-descriptions>

          <div class="border-t pt-4">
            <h3 class="text-lg font-semibold">Hành động: {{ selectedLog.action }}</h3>

            <!-- Hiển thị thay đổi nếu có diffs -->
            <div v-if="diffs.length" class="mt-6">
              <el-table :data="diffs" style="width: 100%">
                <el-table-column prop="field" label="Trường" />
                <el-table-column prop="before" label="Dữ liệu trước" />
                <el-table-column prop="after" label="Dữ liệu sau" />
              </el-table>
            </div>

            <!-- Hiển thị data_before nếu không có data_after -->
            <div v-else-if="selectedLog.data_before" class="mt-6">
              <h3 class="mb-2 text-base">Thông tin chi tiết</h3>
              <el-table :data="beforeData" style="width: 100%">
                <el-table-column prop="field" label="Trường" />
                <el-table-column prop="value" label="Dữ liệu" />
              </el-table>
            </div>

            <!-- Hiển thị data_after nếu không có data_before -->
            <div v-else-if="selectedLog.data_after" class="mt-6">
              <h3 class="mb-2 text-base">Thông tin chi tiết</h3>
              <el-table :data="afterData" style="width: 100%">
                <el-table-column prop="field" label="Trường" />
                <el-table-column prop="value" label="Dữ liệu" />
              </el-table>
            </div>
          </div>
        </div>
      </template>

      <template #footer>
        <el-button size="large" type="default" @click="handleClose" class="flex-1">
          <el-icon class="mr-1"><Close /></el-icon>
          Đóng
        </el-button>
      </template>
    </Modal>
  </section>
</template>

<script setup>
import { computed, onMounted, ref } from 'vue'
import PageBreadcrumb from '~/components/common/PageBreadcrumb.vue'
import Pagination from '~/components/common/Pagination.vue'
import { SORT_FILTER } from '~/utils/configs/constant.config'
import { useUserLogs } from '@/composables/modules/logs'
import ActionButtons from '~/components/common/ActionButtons.vue'
import Modal from '~/components/common/Modal.vue'

//
const { userLogs, loading, pagination, filters, fetchUserLogs } = useUserLogs()

//
const showViewDetail = ref(false)
const selectedLog = ref(null)
const currentPageTitle = 'User Logs'

// Life sycle
onMounted(() => {
  fetchUserLogs()
})

// Computed
const diffs = computed(() => {
  if (!selectedLog.value) return []

  return getDiffFields(selectedLog.value.data_before, selectedLog.value.data_after)
})

// Computed để hiển thị data_before dưới dạng bảng
const beforeData = computed(() => {
  if (!selectedLog.value || !selectedLog.value.data_before) return []
  return jsonToTableData(selectedLog.value.data_before)
})

// Computed để hiển thị data_after dưới dạng bảng
const afterData = computed(() => {
  if (!selectedLog.value || !selectedLog.value.data_after) return []
  return jsonToTableData(selectedLog.value.data_after)
})

// Watch

// thay đổi số trang
const handlePageChange = async (newPage) => {
  filters.page = newPage
  await fetchUserLogs()
}

// thay đổi số bản ghi trong 1 trang
const handlePerPageChange = async (newSize) => {
  filters.limit = newSize
  filters.page = 1
  await fetchUserLogs()
}

// Xử lý tìm kiếm
// v-mode: binding dữ liệu 2 chiều
// event listener, xử lý thủ công
const handleSearch = (value) => {
  filters.page = 1
  fetchUserLogs()
}

// Lắng nghe sự kiện sort-change trên el-table
// sortable= "'custom'" chặn không tự sort, giao việc cho handleSort xử lý
const handleSortChange = ({ prop, order }) => {
  filters.sort_by = prop
  filters.sort_order = order === 'ascending' ? SORT_FILTER.ASC : SORT_FILTER.DESC
  fetchUserLogs()
}

// View log detail
const handleViewDetail = (id) => {
  const logDetail = userLogs.value.find((log) => log.id === id)
  if (logDetail) {
    selectedLog.value = logDetail
    showViewDetail.value = true
  }
}

const handleClose = () => {
  showViewDetail.value = false
}

// fomat
const parseJson = (str) => {
  try {
    return JSON.parse(str)
  } catch {
    return null
  }
}

// So sánh sự thay đổi giữa before và after
const getDiffFields = (beforeStr, afterStr) => {
  const before = parseJson(beforeStr)
  const after = parseJson(afterStr)

  if (!before || !after) return []

  const diffs = []

  for (const key in before) {
    if (before[key] !== after[key]) {
      diffs.push({
        field: key,
        before: before[key] ?? 'N/A',
        after: after[key] ?? 'N/A',
      })
    }
  }

  return diffs
}

// Chuyển JSON thành mảng key-value cho bảng
const jsonToTableData = (jsonStr) => {
  const json = parseJson(jsonStr)
  if (!json) return []

  return Object.entries(json).map(([field, value]) => ({
    field,
    value: value ?? 'N/A',
  }))
}

// Enhanced table styles based on existing patterns
const getTableHeaderStyle = () => {
  return {
    backgroundColor: 'var(--el-bg-color-page)',
    color: 'var(--el-text-color-primary)',
    textAlign: 'center',
    fontWeight: '600',
    fontSize: '14px',
    padding: '16px 12px',
    borderBottom: '1px solid var(--el-border-color-light)',
    textTransform: 'uppercase',
    letterSpacing: '0.5px',
  }
}
</script>

<style scoped>
/* Styles có thể được thêm vào đây nếu cần */
.search-input :deep(.el-input__wrapper) {
  height: 38px;
  display: flex;
  align-items: center;
}

.search-input :deep(.el-input__inner) {
  height: 100%;
}

/** dark mode */
.dark .el-table {
  --el-table-bg-color: rgb(31 41 55);
  --el-table-tr-bg-color: rgb(31 41 55);
  --el-table-header-bg-color: rgb(55 65 81);
  background-color: rgb(31 41 55) !important;
}

.dark .el-table__body-wrapper {
  background-color: rgb(31 41 55) !important;
}

.dark .el-scrollbar__view {
  background-color: rgb(31 41 55) !important;
}

:deep(.el-descriptions__body .el-descriptions__table.is-bordered .el-descriptions__cell) {
  border-color: #ebeef5;
  padding: 8px 11px;
}

:deep(.el-descriptions__label.el-descriptions__cell.is-bordered-label) {
  background-color: #f9fafb;
  color: #344054;
  font-weight: 500;
}
</style>
