import { GridIcon, UserCircleIcon, BoxCubeIcon, CreditCardIcon, HistoryIcon } from '@/components/icons/index.js'

export const menuConfig = [
  {
    id: 'dashboard',
    title: 'Tổng Quan',
    icon: GridIcon,
    route: { name: 'overview' },
    permissions: ['dashboard.view'],
    badge: null,
  },
  {
    id: 'users',
    title: 'Quản lý người dùng',
    icon: UserCircleIcon,
    permissions: ['role_management.view', 'permission_management.view'],
    children: [
      {
        id: 'users-list',
        title: '<PERSON><PERSON> sách người dùng',
        icon: null,
        route: { name: 'users' },
        permissions: ['user_management.view'],
        badge: null,
      },
      {
        id: 'users-roles',
        title: '<PERSON>ai trò & quyền hạn',
        icon: UserCircleIcon,
        route: { name: 'roles' },
        permissions: ['role_management.view', 'permission_management.view'],
        badge: null,
      },
    ],
  },
  {
    id: 'cms',
    title: '<PERSON>u<PERSON><PERSON> lý <PERSON>',
    icon: BoxCubeIcon,
    permissions: [],
    children: [
      {
        id: 'cms-menus',
        title: '<PERSON>u<PERSON><PERSON> lý <PERSON>',
        icon: null,
        route: { name: 'cms-menus' },
        permissions: ['menu_management.view' , 'menu_item_management.view'],
        badge: null,
      },
      {
        id: 'cms-posts',
        title: 'Quản lý Bài viết',
        icon: null,
        route: { name: 'cms-posts' },
        permissions: ['post_management.view'],
        badge: null,
      },
      {
        id: 'cms-categories',
        title: 'Quản lý Danh mục',
        icon: null,
        route: { name: 'cms-categories' },
        permissions: ['category_management.view'],
        badge: null,
      },
      {
        id: 'cms-static-pages',
        title: 'Quản lý trang tĩnh',
        icon: BoxCubeIcon,
        route: { name: 'cms-static-pages' },
        permissions: ['static_page_management.view'],
        badge: null,
      },
      {
        id: 'gallery',
        title: 'Quản lý Gallery',
        icon: BoxCubeIcon,
        route: { name: 'cms-gallery' },
        permissions: ['gallery_group_management.view' , 'gallery_item_management.view'],
        badge: null,
      },
      {
        id: 'scripts',
        title: 'Quản lý Script',
        icon: BoxCubeIcon,
        route: { name: 'scripts' },
        permissions: ['script_management.view'],
        badge: null,
      },
      {
        id: 'cms-site-setting-groups',
        title: 'Cấu hình website',
        icon: null,
        route: { name: 'cms-site-setting-groups' },
        permissions: ['site_setting_group_management.view' , 'site_setting_management.view'],
        badge: null,
      },
    ],
  },
  {
    id: 'logs',
    title: 'Logs',
    icon: HistoryIcon,
    permissions: [],
    children: [
      {
        id: 'user-logs',
        title: 'Lịch sử hoạt động',
        icon: null,
        route: { name: 'user-logs' },
        permissions: ['user_log_management.view'],
        badge: null,
      },
    ],
  },
]

// Helper function để lấy menu item theo route
export function getMenuItemByRoute(routeName, menuItems = menuConfig) {
  for (const item of menuItems) {
    if (item.route?.name === routeName) return item
    if (item.children) {
      const childItem = getMenuItemByRoute(routeName, item.children)
      if (childItem) return childItem
    }
  }
  return null
}

export default menuConfig
