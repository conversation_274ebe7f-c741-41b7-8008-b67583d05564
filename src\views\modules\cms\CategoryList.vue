<template>
  <div class="category-list-wrapper">
    <PageBreadcrumb :page-title="currentPageTitle" :breadcrumbs="[{ label: 'Quản lý CMS', to: '/cms' }]" />

    <div class="rounded-2xl border border-gray-200 bg-white p-5 lg:p-6 dark:border-gray-800 dark:bg-white/[0.03]">
      <!-- Filters -->
      <div class="mb-6 rounded-lg border border-gray-200 bg-gray-50 p-6 dark:border-gray-700 dark:bg-gray-900/50">
        <div class="flex flex-wrap items-center gap-4">
          <div class="flex items-center gap-2">
            <el-input
              v-model="searchTerm"
              placeholder="Tìm kiếm theo tên danh mục..."
              :prefix-icon="SearchIcon"
              @clear="handleResetFilters"
              clearable
              style="width: 300px"
              size="large"
            />
          </div>
          <div class="flex items-center gap-2">
            <el-select
              v-model="selectedStatus"
              placeholder="Lọc theo trạng thái"
              @change="handleStatusFilter"
              clearable
              style="width: 200px"
              size="large"
            >
              <el-option
                v-for="option in statusOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </div>
          <div class="ml-auto flex items-center gap-3">
            <ButtonCommon type="primary" @click="showCreateModal = true" v-if="canCreateCategory" :icon="PlusIcon">
              Tạo Danh mục
            </ButtonCommon>
          </div>
        </div>
      </div>

      <!-- Categories Table -->
      <div
        class="table-container overflow-hidden rounded-lg border border-gray-200 bg-white dark:border-gray-700 dark:bg-gray-800"
        style="overflow-x: auto;"
      >
        <el-table
          v-loading="loading"
          :data="categories"
          row-key="id"
          style="width: 100%"
          :header-cell-style="getTableHeaderStyle"
          :cell-style="getTableCellStyle"
          table-layout="auto"
          class="categories-table"
          :row-class-name="getRowClassName"
          empty-text="Chưa có danh mục nào"
        >
         <!-- Name Column -->
        <el-table-column label="Tên danh mục" min-width="150" align="left">
          <template #default="{ row }">
            <div class="flex items-center gap-2 p-2">
              <span class="font-medium text-gray-900 dark:text-white line-clamp-1">{{ row.name }}</span>
            </div>
          </template>
        </el-table-column>

        <!-- Slug Column -->
        <el-table-column label="Slug" width="140" align="center">
          <template #default="{ row }">
            <el-tag type="info" size="small">{{ row.slug }}</el-tag>
          </template>
        </el-table-column>

        <!-- Description Column -->
        <el-table-column label="Mô tả" min-width="150" show-overflow-tooltip>
          <template #default="{ row }">
            <div class="p-2">
              <span class="text-sm text-gray-600 dark:text-gray-400 line-clamp-2">
                {{ row.description || '—' }}
              </span>
            </div>
          </template>
        </el-table-column>

        <!-- Status Column -->
        <el-table-column label="Trạng thái" width="150" align="center">
          <template #default="{ row }">
            <el-switch
              v-model="row.status"
              :active-value="1"
              :inactive-value="0"
              @change="handleStatusChange(row.id, 'status', $event)"
            />
          </template>
        </el-table-column>

        <!-- Featured Column -->
        <el-table-column label="Nổi bật" width="110" align="center">
          <template #default="{ row }">
            <el-switch
              v-model="row.is_featured"
              :active-value="1"
              :inactive-value="0"
              @change="handleStatusChange(row.id, 'is_featured', $event)"
            />
          </template>
        </el-table-column>

        <!-- Order Column -->
        <el-table-column label="Thứ tự" width="120" align="center">
          <template #default="{ row }">
            <div class="text-center text-sm text-gray-600 dark:text-gray-300">
              {{ row.order }}
            </div>
          </template>
        </el-table-column>

        <!-- Actions Column -->
        <el-table-column label="Thao tác" width="200" fixed="right" align="center">
          <template #default="{ row }">
            <ActionButtons
              :show-view="false"
              :show-edit="canEditCategory"
              :show-delete="canDeleteCategory"
              @edit="editCategory(row)"
              @delete="deleteCategory(row.id)"
            />
          </template>
        </el-table-column>

        </el-table>
      </div>

      <!-- Pagination -->
      <Pagination :pagination="pagination" @page-change="handlePageChange" @per-page-change="handlePerPageChange" />

      <!-- Create/Edit Modal -->
      <CategoryFormModal
        v-model:visible="showCreateModal"
        :category="null"
        :categories="categories"
        @success="handleModalSuccess"
      />

      <CategoryFormModal
        v-model:visible="showEditModal"
        :category="selectedCategory"
        :categories="categories"
        @success="handleModalSuccess"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { Search } from '@element-plus/icons-vue'
import { useCategories } from '@/composables/modules/cms/useCategories.js'
import { PlusIcon } from '@/components/icons/index.js'
import PageBreadcrumb from '@/components/common/PageBreadcrumb.vue'
import CategoryFormModal from '@/components/modules/cms/categories/CategoryFormModal.vue'
import Pagination from '@/components/common/Pagination.vue'
import ActionButtons from '@/components/common/ActionButtons.vue'
import ButtonCommon from '@/components/common/ButtonCommon.vue'
import { hasPermission, getUserPermissions } from '@/utils/helpers/permission.helper.js'
import { useAuthStore } from '@/state/index.js'
import { storeToRefs } from 'pinia'

// Icons
const SearchIcon = Search

// Page title
const currentPageTitle = ref('Quản lý Danh mục')

// Composables
const {
  loading,
  categories,
  pagination,
  fetchCategories,
  deleteCategory: deleteCategoryApi,
  updateCategoryStatus,
  searchCategories,
  filterByStatus,
  resetFilters,
  handlePageChange,
  handlePerPageChange,
  getStatusOptions,
} = useCategories()

// Modal states
const showCreateModal = ref(false)
const showEditModal = ref(false)
const selectedCategory = ref(null)

// Filter states
const searchTerm = ref('')
const selectedStatus = ref('')

// Computed
const statusOptions = computed(() => {
  return getStatusOptions()
})

// Enhanced table styles based on PostList and UserList patterns
const getTableHeaderStyle = () => {
  return {
    backgroundColor: 'var(--el-bg-color-page)',
    color: 'var(--el-text-color-primary)',
    textAlign: 'center',
    fontWeight: '600',
    fontSize: '14px',
    padding: '16px 12px',
    borderBottom: '1px solid var(--el-border-color-light)',
    textTransform: 'uppercase',
    letterSpacing: '0.5px',
  }
}

const getTableCellStyle = () => {
  return {
    backgroundColor: 'var(--el-bg-color)',
    color: 'var(--el-text-color-primary)',
    borderBottom: '1px solid var(--el-border-color-lighter)',
    padding: '16px 12px',
    fontSize: '14px',
  }
}

const getRowClassName = ({ rowIndex }) => {
  return rowIndex % 2 === 0 ? 'even-row' : 'odd-row'
}

// Methods
const handleStatusFilter = async () => {
  await filterByStatus(selectedStatus.value)
}

const handleResetFilters = async () => {
  searchTerm.value = ''
  selectedStatus.value = ''
  await resetFilters()
}

const handleStatusChange = async (id, field, value) => {
  await updateCategoryStatus(id, field, value)
}

const editCategory = (category) => {
  selectedCategory.value = category
  showEditModal.value = true
}

const deleteCategory = async (id) => {
  await deleteCategoryApi(id)
}

const handleModalSuccess = () => {
  showCreateModal.value = false
  showEditModal.value = false
  selectedCategory.value = null
  // Refresh the list after successful operation
  fetchCategories()
}

// Auto search when input changes
let searchTimeout = null
watch(searchTerm, (newValue) => {
  if (searchTimeout) {
    clearTimeout(searchTimeout)
  }

  searchTimeout = setTimeout(async () => {
    if (newValue && newValue.trim()) {
      await searchCategories(newValue.trim())
    } else {
      await resetFilters()
    }
  }, 300) // 300ms delay for faster response
})

// Initialize
onMounted(() => {
  fetchCategories()
})

const authStore = useAuthStore()
const { authUser } = storeToRefs(authStore)
const userPermissions = getUserPermissions(authUser.value)

const canCreateCategory = hasPermission(['category_management.create'], userPermissions)
const canEditCategory = hasPermission(['category_management.edit'], userPermissions)
const canDeleteCategory = hasPermission(['category_management.delete'], userPermissions)
</script>

<style lang="scss" scoped>
/* Enhanced table styling */
:deep(.categories-table) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.categories-table .even-row) {
  background-color: var(--el-bg-color);
}

:deep(.categories-table .odd-row) {
  background-color: var(--el-fill-color-light);
}

:deep(.categories-table .even-row:hover),
:deep(.categories-table .odd-row:hover) {
  background-color: var(--el-fill-color) !important;
}

:deep(.categories-table .el-table__header-wrapper) {
  background-color: var(--el-bg-color-page);
}

:deep(.categories-table .el-table__row) {
  transition: background-color 0.2s ease;
}

:deep(.categories-table .el-table__cell) {
  border-color: var(--el-border-color-lighter);
}

/* Responsive table improvements */
:deep(.categories-table .el-table__row) {
  height: auto;
  min-height: 60px;
}

:deep(.categories-table .el-table__cell) {
  vertical-align: middle;
}

/* Table empty state styling */
:deep(.categories-table .el-table__empty-block) {
  background-color: var(--el-bg-color);
  border: none;
  padding: 40px 20px;
}

:deep(.categories-table .el-table__empty-text) {
  color: var(--el-text-color-regular);
  font-size: 14px;
  font-weight: 500;
}

/* Dark mode empty state */
.dark :deep(.categories-table .el-table__empty-block) {
  background-color: #1e293b;
}

.dark :deep(.categories-table .el-table__empty-text) {
  color: #94a3b8;
}

/* Ensure proper responsive behavior */
:deep(.categories-table .el-table__body-wrapper) {
  overflow-x: auto;
}

:deep(.categories-table .el-table__fixed-right) {
  box-shadow: -2px 0 8px rgba(0, 0, 0, 0.1);
}

/* Switch styling */
:deep(.categories-table .el-switch) {
  --el-switch-on-color: #409eff;
  --el-switch-off-color: #dcdfe6;
}

/* Text truncation utilities */
.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
