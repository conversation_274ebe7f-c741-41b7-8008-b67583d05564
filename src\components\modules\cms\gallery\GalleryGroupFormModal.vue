<!-- <PERSON><PERSON> thêm mới/chỉnh sửa nhóm gallery -->
<template>
  <el-dialog
    :model-value="isVisible"
    :title="groupData ? 'Chỉnh sửa Gallery' : 'Thêm mới Gallery'"
    width="600px"
    @close="close"
  >
    <div class="p-6">
      <!-- Form nhập thông tin nhóm gallery -->
      <el-form ref="formRef" :model="form" label-position="top" @submit.prevent="submitForm">
        <!-- Tên gallery -->
        <el-form-item
          label="Tên Gallery"
          prop="name"
          style="margin-bottom: 12px"
          :rules="[{ required: true, message: 'Tên gallery là bắt buộc', trigger: 'blur' }]"
        >
          <el-input v-model="form.name" placeholder="Nhập tên gallery" />
        </el-form-item>

        <!-- Location key (sinh tự động từ tên) -->
        <el-form-item
          label="Location Key (ví dụ: loaction-key)"
          prop="location_key"
          style="margin-top: 24px; margin-bottom: 12px"
          :rules="[
            { required: true, message: '<PERSON>ui lòng nhập Location key', trigger: 'blur' },
            { min: 2, max: 255, message: 'Location key phải từ 2-255 ký tự', trigger: 'blur' },
            {
              pattern: /^[a-zA-Z0-9-]+$/,
              message: 'Location key chỉ được chứa chữ cái, số và dấu gạch ngang',
              trigger: 'blur',
            },
          ]"
        >
          <el-input v-model="form.location_key" placeholder="Tự động tạo từ tên gallery hoặc có thể nhập">
            <template #append>
              <el-button class="shadow-md" type="primary" @click="copyLocationKey">Copy</el-button>
            </template>
          </el-input>
          <div class="mt-1 text-sm text-gray-500">
            Loaction key phải là duy nhất và chỉ chứa chữ cái, số và dấu gạch ngang
          </div>
        </el-form-item>

        <!-- Mô tả -->
        <el-form-item label="Mô tả" prop="description" style="margin-top: 24px; margin-bottom: 12px">
          <el-input v-model="form.description" type="textarea" :rows="3" placeholder="Nhập mô tả" />
        </el-form-item>

        <!-- Vị trí hiển thị -->
        <el-form-item label="Vị trí hiển thị" prop="location_display" style="margin-top: 24px; margin-bottom: 12px">
          <el-select v-model="form.location_display" placeholder="Chọn vị trí" clearable>
            <el-option label="Homepage" :value="1" />
            <el-option label="Landing" :value="2" />
            <el-option label="Both" :value="3" />
          </el-select>
        </el-form-item>
      </el-form>
    </div>

    <!-- Footer modal -->
    <ButtonModalCommon
      :loading="loading"
      :can-submit="true"
      cancel-text="Hủy"
      :submit-text="groupData ? 'Cập nhật' : 'Thêm mới'"
      @cancel="close"
      @submit="submitForm"
    />
  </el-dialog>
</template>

<script setup>
import { watch, reactive, ref, nextTick } from 'vue'
import { generateSlugWithDash } from '@/utils/helpers/string.helper.js'
import { ElMessage } from 'element-plus'
import ButtonModalCommon from '@/components/common/ButtonModalCommon.vue'

/**
 * Props component
 */
const props = defineProps({
  isVisible: Boolean, // Điều khiển hiển thị modal
  groupData: Object, // Dữ liệu nhóm (null: thêm mới, object: chỉnh sửa)
})

/**
 * Emit các sự kiện cho component cha
 */
const emit = defineEmits(['close', 'save'])

/**
 * Tham chiếu đến form để validate
 */
const formRef = ref(null)
const isEditMode = ref(false)

/**
 * Khởi tạo form với giá trị mặc định
 */
const defaultFormData = {
  id: null,
  name: '',
  location_key: '',
  description: '',
  location_display: 1,
}

const form = reactive({ ...defaultFormData })

/**
 * Reset form về trạng thái ban đầu
 */
const resetForm = () => {
  // Method 1: Reset từng field
  Object.keys(defaultFormData).forEach((key) => {
    form[key] = defaultFormData[key]
  })

  // Method 2: Clear validation
  nextTick(() => {
    formRef.value?.clearValidate()
  })
}

/**
 * Populate form với dữ liệu từ groupData
 */
const populateForm = (data) => {
  if (data) {
    isEditMode.value = true
    form.id = data.id
    form.name = data.name || ''
    form.location_key = data.location_key || ''
    form.description = data.description || ''
    form.location_display = data.location_display || 1
  } else {
    isEditMode.value = false
    resetForm()
  }
}

/**
 * Tự động sinh location_key từ tên gallery
 */
watch(
  () => form.name,
  (newName) => {
    if (!isEditMode.value) {
      form.location_key = generateSlugWithDash(newName)
    }
  },
)

/**
 * Cập nhật form khi groupData thay đổi
 * - Nếu có dữ liệu: mode chỉnh sửa
 * - Nếu null: mode thêm mới
 */
watch(
  () => props.groupData,
  (newData) => {
    populateForm(newData)
  },
  { immediate: true, deep: true },
)

/**
 * Đóng modal và thông báo cho component cha
 */
const close = () => {
  resetForm()
  emit('close')
}

/**
 * Xử lý submit form
 * - Validate trước khi gửi dữ liệu
 * - Emit sự kiện save với dữ liệu đã validate
 */
const submitForm = async () => {
  if (!formRef.value) return
  await formRef.value.validate((valid) => {
    if (valid) {
      emit('save', { ...form })
    }
  })
}

/**
 * Copy giá trị location_key vào clipboard
 */
const copyLocationKey = async () => {
  if (!form.location_key) {
    ElMessage.success('Không có dữ liệu để copy')
    return
  }
  try {
    await navigator.clipboard.writeText(form.location_key)
    ElMessage.success('Copy thành công')
  } catch (err) {
    console.error('Lỗi copy loaction_key:', err)
    ElMessage.success('Copy không thành công')
  }
}
</script>

<style scoped>
/* Kiểu dáng tùy chỉnh nếu cần */
/* Footer Button Styles */
.footer-btn {
  min-width: 80px;
  font-weight: 500;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.cancel-btn {
  background: #f5f5f5 !important;
  border: 1px solid #d9d9d9 !important;
  color: #666 !important;
}

.cancel-btn:hover {
  background: #e6e6e6 !important;
  border-color: #bfbfbf !important;
  color: #333 !important;
}

.submit-btn {
  background: #1890ff !important;
  border: 1px solid #1890ff !important;
  color: #fff !important;
}

.submit-btn:hover {
  background: #40a9ff !important;
  border-color: #40a9ff !important;
}
</style>
