<template>
  <div class="">
    <!-- Breadcrumb navigation -->
    <PageBreadcrumb :page-title="currentPageTitle" :breadcrumbs="[{ label: 'Quản lý CMS', to: '/cms' }]" />

    <div class="rounded-2xl border border-gray-200 bg-white p-5 lg:p-6 dark:border-gray-800 dark:bg-white/[0.03]">
      <!-- Tạo column layout: 4:6 -->
      <div class="grid grid-cols-1 gap-6 lg:h-[650px] lg:grid-cols-10 lg:items-stretch">
        <!-- Column 1: (4/10 width) -->
        <div class="h-full lg:col-span-4">
          <div class="flex h-full flex-col">
            <!-- Header với tìm kiếm và nút thêm -->
            <div class="flex items-center gap-3">
              <el-input placeholder="Tìm kiếm gallery..." clearable class="search-input flex-1" v-model="searchQuery">
                <template #prefix>
                  <el-icon><Search /></el-icon>
                </template>
              </el-input>

              <!-- Thêm mới gallery -->
              <ButtonCommon
                text="Thêm Gallery"
                type="primary"
                size="medium"
                :icon="PlusIcon"
                @click="openAddGroupModal"
              />
            </div>

            <!-- Search results Info -->
            <p v-if="filteredGalleries.length > 0 && searchQuery !== ''" class="mt-2 text-sm text-gray-500">
              Tìm thấy {{ filteredGalleries.length }} nhóm Gallery
            </p>

            <!--Groups list-->
            <div
              class="mt-5 h-auto max-h-[400px] flex-1 overflow-hidden rounded-lg border border-gray-200 bg-gray-50 lg:h-[calc(100%-60px)] lg:max-h-[580px] dark:border-gray-700 dark:bg-gray-900/50"
            >
              <div v-loading="loading" class="p-2">
                <!-- Scrollable list -->
                <div class="scroll-thin max-h-[400px] space-y-2 overflow-y-auto pr-1 pb-2.5 lg:max-h-[580px]">
                  <div
                    v-for="group in filteredGalleries"
                    :key="group.id"
                    class="mb-2 flex cursor-pointer items-center justify-between rounded-lg border p-3 transition-all duration-200 hover:shadow-md dark:hover:shadow-lg"
                    :class="{
                      'border-blue-400 bg-gradient-to-r from-blue-50 to-indigo-50 shadow-lg dark:border-blue-500 dark:from-blue-900/30 dark:to-indigo-900/30 dark:shadow-blue-500/20':
                        selectedGroup?.id === group.id,
                      'border-gray-200 bg-white hover:border-gray-300 dark:border-gray-600 dark:bg-gray-800 dark:hover:border-gray-500':
                        selectedGroup?.id !== group.id,
                    }"
                    @click="handleSelectedGroup(group)"
                  >
                    <!-- Group info -->
                    <div class="flex-1">
                      <div class="flex items-center gap-2">
                        <h5 class="max-w-[220px] text-base font-medium text-gray-900 dark:text-white">
                          {{ group.name || 'Không xác đinh' }}
                        </h5>
                        <!-- Selected indicator -->
                        <div v-if="selectedGroup?.id === group.id" class="flex items-center gap-1">
                          <div class="h-2 w-2 animate-pulse rounded-full bg-blue-500"></div>
                          <span class="text-sm font-medium text-blue-600 dark:text-blue-400"> Đang chọn </span>
                        </div>
                      </div>
                      <p class="mt-1 line-clamp-2 text-sm text-gray-400 dark:text-gray-500" v-if="group.description">
                        {{ group.description }}
                      </p>
                    </div>

                    <!-- Action buttons -->
                    <div class="flex items-center gap-2">
                      <ButtonCommon
                        :icon="EditIcon"
                        type="info"
                        size="small"
                        rounded
                        tooltip="Chỉnh sửa nhóm"
                        @click.stop="openEditGroupModal(group)"
                      />
                      <ButtonCommon
                        :icon="TrashIcon"
                        type="danger"
                        size="small"
                        rounded
                        tooltip="Xóa nhóm"
                        @click.stop="handleDeleteGroup(group.id)"
                      />
                    </div>
                  </div>

                  <!-- Check có group không-->
                  <div v-if="filteredGalleries.length === 0" class="py-8 text-center text-gray-500 dark:text-gray-400">
                    <div class="text-lg">🔍</div>
                    <p class="font-medium">
                      {{ searchQuery ? 'Không tìm thấy nhóm nào' : 'Không có nhóm nào' }}
                    </p>
                    <p class="text-sm">
                      {{ searchQuery ? `Không có nhóm nào phù hợp với "${searchQuery}"` : 'Hãy tạo nhóm đầu tiên' }}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Column 10: (6/10 width) -->
        <div class="w-full lg:col-span-6">
          <div
            class="flex h-[640px] flex-col overflow-hidden rounded-[0.5rem] border border-[#e5e7eb] bg-[#ffffff] dark:border-[#374151] dark:bg-white/[0.03]"
          >
            <!-- Banel header -->
            <div
              class="flex items-center justify-between border-b border-b-[#e5e7eb] bg-[#f9fafb] p-4 dark:border-[#374151] dark:bg-[rgba(17,24,39,0.5)]"
            >
              <h5 class="flex-1 text-base font-semibold dark:text-white">Danh sách hình ảnh của nhóm Gallery</h5>

              <!-- Button thêm mở modal thêm ảnh -->
              <div v-if="galleryItems.length !== 0">
                <ButtonCommon type="primary" size="small" :icon="PlusIcon" text="Thêm Ảnh" @click="openAddItemModal" />
              </div>
            </div>

            <!-- Panel content -->
            <div class="scroll-thin flex-1 overflow-y-auto px-1">
              <div v-if="!selectedGroup" class="flex h-full flex-col items-center justify-center text-center">
                <div class="mb-4 text-5xl">🖼️</div>
                <p class="text-base text-[#6b7280]">Chọn một nhóm Gallery để xem Danh sách hình ảnh</p>
              </div>

              <div v-else-if="loadingGetItemsGallery" class="flex h-full flex-col items-center justify-center">
                <el-icon class="mb-4 animate-spin text-2xl text-gray-400">
                  <Loading />
                </el-icon>

                <p class="text-base text-[#6b7280]">Đang tải dữ liệu ...</p>
              </div>

              <div
                v-else-if="galleryItems.length === 0"
                class="flex h-full flex-col items-center justify-center text-center"
              >
                <div class="mb-4 text-5xl">📝</div>
                <p class="text-base text-[#6b7280]">Chưa có danh sách hình ảnh nào trong nhóm Gallery</p>
                <div class="mt-1.5">
                  <!-- Button thêm mở modal thêm ảnh -->
                  <ButtonCommon
                    type="primary"
                    size="small"
                    :icon="PlusIcon"
                    text="Thêm Ảnh"
                    @click="openAddItemModal"
                  />
                </div>
              </div>

              <div v-else class="gallery-table-container">
                <el-table
                  height="100%"
                  :data="galleryItems"
                  style="min-width: 100%"
                  empty-text="Chưa có hình ảnh nào"
                  class="dark-table"
                  size="small"
                >
                  <el-table-column prop="image_url" label="Hình ảnh" min-width="160" align="center">
                    <template #default="{ row }">
                      <div
                        class="flex h-[100px] w-full cursor-pointer items-center justify-center overflow-hidden bg-gray-100"
                        @click="
                          () => {
                            previewVisible = true
                            previewUrl = row.image_url
                          }
                        "
                      >
                        <img
                          :src="row.image_url"
                          :alt="row.alt_text"
                          class="h-full w-full object-cover object-center"
                        />
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column prop="title" label="Tiêu đề" min-width="180" align="center">
                    <template #default="{ row }">
                      <div class="flex items-center justify-center px-3 py-2">
                        <span
                          class="line-clamp-2 text-center font-medium text-gray-900 dark:text-white"
                          :title="row.title"
                        >
                          {{ row.title }}
                        </span>
                      </div>
                    </template>
                  </el-table-column>

                  <el-table-column prop="status" min-width="120" label="Trạng thái" align="center">
                    <template #default="{ row }">
                      <el-switch
                        v-model="row.status"
                        :active-value="1"
                        :inactive-value="0"
                        @change="(newStatus) => handleToggleStatus(row, newStatus)"
                        size="small"
                      />
                    </template>
                  </el-table-column>
                  <el-table-column label="Thao tác" width="220" align="center" fixed="right">
                    <template #default="{ row }">
                      <ActionButtons
                        :show-view="false"
                        @edit="openEditItemModal(row)"
                        @delete="handleDeleteGalleryItem(row.gallery_group_id, row.id)"
                      />
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </div>

            <!-- Panel footer -->
            <div v-if="galleryItems.length > 0" class="shrink-0 text-center">
              <!-- <p class="text-sm text-[#9ca3af]">Tổng số: {{ totalItems }} hình ảnh của nhóm</p> -->
              <Pagination
                :pagination="pagination"
                @page-change="handlePageChange"
                @per-page-change="handlePerPageChange"
              />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Modal thêm/sửa nhóm gallery -->
    <GalleryGroupFormModal
      :isVisible="isGroupModalVisible"
      :groupData="editingGroup"
      @close="closeGroupModal"
      @save="handleSaveGroup"
    />

    <!-- Modal thêm/sửa ảnh -->
    <GalleryItemFormModal
      :isVisible="isItemModalVisible"
      :itemData="selectedItem"
      @close="closeItemModal"
      @save-item="handleSaveItem"
    />

    <!-- Hiển thị preview hình ảnh -->
    <el-image-viewer v-if="previewVisible" :url-list="[previewUrl]" @close="previewVisible = false" />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { PlusIcon, EditIcon, TrashIcon } from '@/components/icons/index.js'
import { Loading } from '@element-plus/icons-vue'

// Components
import PageBreadcrumb from '@/components/common/PageBreadcrumb.vue'
import GalleryGroupFormModal from '@/components/modules/cms/gallery/GalleryGroupFormModal.vue'
import GalleryItemFormModal from '~/components/modules/cms/gallery/GalleryItemFormModal.vue'

import ButtonCommon from '~/components/common/ButtonCommon.vue'
import ActionButtons from '~/components/common/ActionButtons.vue'
import Pagination from '~/components/common/Pagination.vue'

// Composables
import { useGalleries } from '@/composables/modules/cms/useGallery.js'

// ===== COMPOSABLE SETUP =====
const {
  galleries,
  gallery,
  loading,
  loadingGetItemsGallery,
  galleryItems,
  pagination,
  fetchGalleries,
  createGallery,
  updateGallery,
  deleteGallery,
  fetchGalleryById,
  fetchItemsByGalleryGroupId,
  updateGalleryItem,
  createGalleryItem,
  deleteGalleryItem,
} = useGalleries()

// Page data
const currentPageTitle = 'Quản lý Gallery'

// ===== LIFECYCLE =====
onMounted(async () => {
  await fetchGalleries()

  // Auto select first gallery if available
  if (galleries.value.length > 0 && !selectedGroup.value) {
    await handleSelectedGroup(galleries.value[0])
  }
})

// ===== REACTIVE STATE =====
/** @type {import('vue').Ref<string>} Từ khóa tìm kiếm */
const searchQuery = ref('')

/** @type {import('vue').Ref<Object|null>} Dữ liệu ảnh đang được chọn */
const selectedItem = ref(null)

/** @type {import('vue').Ref<boolean>} Trạng thái hiển thị modal nhóm */
const isGroupModalVisible = ref(false)

/** @type {import('vue').Ref<Object|null>} Dữ liệu nhóm được chọn để chỉnh sửa */
const selectedGroup = ref(null)

/** Group được chọn để cập nhật/ chỉnh sửa */
const editingGroup = ref(null)

/** @type {import('vue').Ref<boolean>} Trạng thái hiển thị modal thêm/sửa ảnh */
const isItemModalVisible = ref(false)

/** Trạng thái hiển thị ảnh preview */
const previewVisible = ref(false)

/** Lưu trữ ảnh muốn preivew */
const previewUrl = ref('')

// ===== COMPUTED =====
/**
 * Danh sách gallery đã được lọc theo từ khóa tìm kiếm
 * @type {import('vue').ComputedRef<Array>}
 */
const filteredGalleries = computed(() => {
  if (!searchQuery.value) {
    return galleries.value
  }
  return galleries.value.filter(
    (gallery) =>
      gallery.name.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
      gallery.description?.toLowerCase().includes(searchQuery.value.toLowerCase()),
  )
})

// ===== WATCHERS =====
/**
 * Theo dõi thay đổi của gallery để cập nhật galleries nếu cần
 * Đảm bảo dữ liệu đồng bộ khi có thay đổi từ gallery items
 */
watch(
  () => gallery.value,
  async (newGallery) => {
    if (newGallery) {
      // Tìm và cập nhật gallery trong danh sách galleries nếu nó đã tồn tại
      const index = galleries.value.findIndex((g) => g.id === newGallery.id)
      if (index !== -1) {
        galleries.value[index] = { ...newGallery }
      } else {
        // Nếu không tìm thấy, tải lại toàn bộ danh sách để đảm bảo dữ liệu đồng bộ
        await fetchGalleries()
      }
    }
  },
  { deep: true },
)

// ===== MODAL METHODS =====
/**
 * Mở modal thêm mới nhóm gallery
 */
const openAddGroupModal = () => {
  editingGroup.value = null
  isGroupModalVisible.value = true
}

/**
 * Mở modal chỉnh sửa nhóm ảnh của
 * @param {Object} group - Dữ liệu nhóm cần chỉnh sửa
 */
const openEditGroupModal = (group) => {
  editingGroup.value = { ...group }
  isGroupModalVisible.value = true
}

/**
 * Đóng modal nhóm gallery
 */
const closeGroupModal = () => {
  isGroupModalVisible.value = false
  editingGroup.value = null
}

/**
 * Mở modal thêm mới ảnh
 */
const openAddItemModal = () => {
  if (!selectedGroup.value) {
    ElMessage.warning('Vui lòng chọn nhóm gallery trước')
    return
  }
  selectedItem.value = {
    image_url: '', // file ảnh
    alt_text: '', // Mô tả ảnh (SEO alt)
    title: '', // Tiêu đề ảnh
    link: '', // Đường dẫn khi click ảnh
    target: '_self', // Target mở link
    status: 1, // 1: hoạt động, 0: không hoạt động
    order: selectedGroup.value.galleryItems?.length ? selectedGroup.value.galleryItems.length + 1 : 1, // Thứ tự hiển thị
  }
  isItemModalVisible.value = true
}

/**
 * Mở modal chỉnh sửa ảnh
 * @param {Object} galleryItem - Dữ liệu ảnh cần chỉnh sửa
 */
const openEditItemModal = (galleryItem) => {
  selectedItem.value = { ...galleryItem }
  isItemModalVisible.value = true
}

/**
 * Đóng modal thêm/chỉnh sửa ảnh
 */
const closeItemModal = () => {
  isItemModalVisible.value = false
  selectedItem.value = null
}

// ===== EVENT HANDLERS =====

/**
 * Thay đổi số trang
 */
const handlePageChange = async (newPage) => {
  if (selectedGroup.value) {
    await fetchItemsByGalleryGroupId(selectedGroup.value.id, {
      page: newPage,
      per_page: pagination.per_page,
    })
  }
}

/**
 * Thay đổi số lượng hiển thị bản ghi trong 1 trang
 */
const handlePerPageChange = async (newSize) => {
  if (selectedGroup.value) {
    await fetchItemsByGalleryGroupId(selectedGroup.value.id, {
      page: 1, // Reset về trang đầu tiên
      per_page: newSize,
    })
  }
}

/**
 * Xử lý khi chọn vào nhóm gallery
 * Khi thay đổi group thì reset lại pagination
 */
const handleSelectedGroup = async (group) => {
  Object.assign(pagination, {
    current_page: 1,
    per_page: 10,
    from: 0,
    to: 0,
    total: 0,
  })

  selectedGroup.value = group
  await fetchItemsByGalleryGroupId(group.id)
}

/**
 * Xử lý lưu nhóm gallery (thêm mới hoặc cập nhật)
 * @param {Object} groupData - Dữ liệu nhóm gallery
 */
const handleSaveGroup = async (groupData) => {
  try {
    if (groupData.id) {
      await updateGallery(groupData.id, groupData)
    } else {
      await createGallery(groupData)
    }

    // Tải lại danh sách sau khi thêm/sửa nhóm
    await fetchGalleries()
    closeGroupModal()
  } catch (error) {
    console.error('Lỗi khi lưu gallery:', error)
    ElMessage.error('Có lỗi xảy ra khi lưu gallery')
  }
}

/**
 * Xử lý lưu thông tin ảnh (thêm mới hoặc cập nhật)
 * @param {Object} itemData - Dữ liệu ảnh cần lưu
 */
const handleSaveItem = async (itemData) => {
  try {
    if (!selectedGroup.value) {
      ElMessage.error('Không tìm thấy nhóm gallery được chọn')
      return
    }

    if (itemData.id) {
      // Nếu có ID thì cập nhật ảnh đã tồn tại
      const updatedItemData = {
        ...itemData,
        _method: 'PUT',
      }

      await updateGalleryItem(selectedGroup.value.id, itemData.id, updatedItemData)
    } else {
      // Nếu không có ID thì thêm mới ảnh
      await createGalleryItem(selectedGroup.value.id, itemData)
    }
    // sau khi create fetch lại data item của group đó
    await fetchItemsByGalleryGroupId(selectedGroup.value.id)
    closeItemModal()
  } catch (error) {
    console.error('Lỗi khi lưu ảnh:', error)
  }
}

/**
 * Xử lý khi xóa nhóm ảnh
 * @param {string|number} groupId - ID của nhóm ảnh cần xóa
 */
const handleDeleteGroup = async (groupId) => {
  try {
    // Kiểm tra xem gallery có còn gallery items không
    await fetchItemsByGalleryGroupId(groupId, { limit: 1 })
    const hasGalleryItems = galleryItems.value && galleryItems.value.length > 0

    if (hasGalleryItems) {
      ElMessage.error('Không thể xóa gallery. Vui lòng xóa hết gallery items trước khi xóa gallery.')
      return false
    }

    await ElMessageBox.confirm('Bạn có chắc chắn muốn xóa nhóm ảnh này không?', 'Xác nhận xóa', {
      confirmButtonText: 'Xóa',
      cancelButtonText: 'Hủy',
      type: 'warning',
    })
    await deleteGallery(groupId)

    if (selectedGroup.value && selectedGroup.value.id === groupId) {
      selectedGroup.value = null
      galleryItems.value = []
    }

    // Reload galleries list
    await fetchGalleries()
  } catch (error) {
    if (error === 'cancel') {
      ElMessage.info('Đã hủy thao tác xóa.')
    } else {
      console.error('Lỗi khi xóa nhóm:', error)
    }
  }
}

/**
 * Xử lý khi xóa ảnh
 * @param {string|number} groupId - ID của nhóm ảnh
 * @param {string|number} itemId - ID của ảnh cần xóa
 */
const handleDeleteGalleryItem = async (groupId, itemId) => {
  try {
    await ElMessageBox.confirm('Bạn có chắc chắn muốn xóa ảnh này không?', 'Xác nhận xóa', {
      confirmButtonText: 'Xóa',
      cancelButtonText: 'Hủy',
      type: 'warning',
    })
    await deleteGalleryItem(groupId, itemId)

    // fetch lại danh sách item sau khi xóa
    if (selectedGroup.value) {
      await fetchItemsByGalleryGroupId(selectedGroup.value.id)
    }
  } catch (error) {
    if (error === 'cancel') {
      ElMessage.info('Đã hủy thao tác xóa.')
    } else {
      console.error('Lỗi khi xóa ảnh:', error)
    }
  }
}

/**
 * xử lý cập nhật status của item
 */
const handleToggleStatus = async (item, newStatus) => {
  try {
    if (!selectedGroup.value) {
      ElMessage.error('Không tìm thấy nhóm gallery được chọn')
      return
    }
    const data = { ...item, status: newStatus ? 1 : 0, _method: 'PUT' }

    await updateGalleryItem(selectedGroup.value.id, item.id, data)

    // Refresh items list to get updated data
    await fetchItemsByGalleryGroupId(selectedGroup.value.id)
  } catch (error) {
    console.error('Error toggling setting status:', error)
    ElMessage.error('Có lỗi xảy ra khi cập nhật trạng thái')
  }
}

const getTypeColor = (type) => {
  const colors = {
    0: 'danger',
    1: 'primary',
  }
  return colors[type] || 'primary'
}

const getStatusText = (status) => {
  return status === 1 ? 'Hiển thị' : 'Ẩn'
}
</script>

<style scoped>
/* Styles có thể được thêm vào đây nếu cần */
.search-input :deep(.el-input__wrapper) {
  height: 38px;
  display: flex;
  align-items: center;
}

.search-input :deep(.el-input__inner) {
  height: 100%;
}

.scroll-thin::-webkit-scrollbar {
  width: 6px; /* Chiều rộng thanh cuộn */
}

.scroll-thin::-webkit-scrollbar-thumb {
  background-color: rgba(100, 100, 100, 0.5);
  border-radius: 4px;
}

.scroll-thin::-webkit-scrollbar-track {
  background-color: transparent;
}

.gallery-table-container {
  height: 100%;
  overflow: auto;
  padding: 12px 8px !important; /* Increased from default 7px to 12px for more spacing */
}

.gallery-table-container :deep(.el-table) {
  font-size: 0.9375rem;
}

.gallery-table-container :deep(.el-table th),
.gallery-table-container :deep(.el-table td) {
  font-size: 0.9375rem;
  padding: 12px 8px !important; /* Increased from default 7px to 12px for more spacing */
}

/** dark mode */
.dark .el-table {
  --el-table-bg-color: rgb(31 41 55);
  --el-table-tr-bg-color: rgb(31 41 55);
  --el-table-header-bg-color: rgb(31 41 55) !important;
  background-color: rgb(31 41 55) !important;
}

.dark .el-table__body-wrapper {
  background-color: rgb(31 41 55) !important;
}

.dark .el-scrollbar__view {
  background-color: rgb(31 41 55) !important;
}
</style>
