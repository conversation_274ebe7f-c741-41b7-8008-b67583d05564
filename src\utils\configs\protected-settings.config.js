/*
  Protected Settings Configuration
  Cấu hình các nhóm cài đặt và cài đặt được bảo vệ không thể xóa
  These settings are critical for system functionality and cannot be deleted by users.
*/

// Protected Setting Groups - These groups cannot be deleted
// Các nhóm cài đặt được bảo vệ - không thể xóa
// Sử dụng group_key để khớp với cấu trúc database
export const PROTECTED_SETTING_GROUPS = [
  {
    group_key: 'general-info',  // Group key used in database (group_key field)
    name: 'General Information', // Display name (English)
    displayName: 'Thông tin chung', // Display name (Vietnamese)
    reason: 'Đây là nhóm cài đặt quan trọng của hệ thống'
  },
]

// Protected Settings - These individual settings cannot be deleted
// Các cài đặt cá nhân được bảo vệ - không thể xóa
// Sử dụng key và group_key để khớp với cấu trúc database
export const PROTECTED_SETTINGS = [
  {
    key: 'site-logo1',
    name: 'Site Logo',
    displayName: 'Logo trang web',
    group_key: 'general-info',
    reason: 'Logo trang web là yếu tố quan trọng để định danh thương hiệu và không thể xóa'
  },
  {
    key: 'site-favicon',
    name: 'Favicon',
    displayName: 'Biểu tượng trang web',
    group_key: 'general-info',
    reason: 'Biểu tượng trang web là yếu tố quan trọng để định danh thương hiệu và không thể xóa'
  },
  {
    key: 'site-name',
    name: 'Site Name',
    displayName: 'Tên trang web',
    group_key: 'general-info',
    reason: 'Tên trang web là thông tin cơ bản của trang web và không thể xóa'
  },
]

// Helper Functions
// Các hàm hỗ trợ

/**
 * Check if a setting group is protected (cannot be deleted)
 * Kiểm tra xem nhóm cài đặt có được bảo vệ không (không thể xóa)
 * @param {string} groupKey - The group key to check (group_key field)
 * @param {string} groupName - The group name to check (name field)
 * @returns {boolean} - True if the group is protected
 */
export const isProtectedSettingGroup = (groupKey, groupName = '') => {
  if (!groupKey && !groupName) return false
  
  return PROTECTED_SETTING_GROUPS.some(group => 
    group.group_key === groupKey || 
    group.name === groupName || 
    group.displayName === groupName
  )
}

/**
 * Check if a setting group is protected by name
 * Kiểm tra xem nhóm cài đặt có được bảo vệ theo tên không
 * @param {string} groupName - The group name to check
 * @returns {boolean} - True if the group is protected
 */
export const isProtectedSettingGroupByName = (groupName) => {
  if (!groupName) return false
  return PROTECTED_SETTING_GROUPS.some(group => 
    group.name === groupName || group.displayName === groupName
  )
}

/**
 * Check if an individual setting is protected (cannot be deleted)
 * Kiểm tra xem cài đặt cá nhân có được bảo vệ không (không thể xóa)
 * @param {string} settingKey - The setting key to check
 * @param {string} settingName - The setting name to check (name field)
 * @returns {boolean} - True if the setting is protected
 */
export const isProtectedSetting = (settingKey, settingName = '') => {
  if (!settingKey && !settingName) return false
  
  return PROTECTED_SETTINGS.some(setting => 
    setting.key === settingKey || 
    setting.name === settingName || 
    setting.displayName === settingName
  )
}

/**
 * Check if a setting is protected by key within a specific group
 * Kiểm tra xem cài đặt có được bảo vệ theo key trong nhóm cụ thể không
 * @param {string} settingKey - The setting key to check
 * @param {string} groupKey - The group key to check within
 * @returns {boolean} - True if the setting is protected
 */
export const isProtectedSettingInGroup = (settingKey, groupKey) => {
  if (!settingKey || !groupKey) return false
  return PROTECTED_SETTINGS.some(setting => 
    setting.key === settingKey && setting.group_key === groupKey
  )
}

/**
 * Get protected setting info by key
 * Lấy thông tin cài đặt được bảo vệ theo key
 * @param {string} settingKey - The setting key
 * @returns {object|null} - Protected setting info or null
 */
export const getProtectedSettingInfo = (settingKey) => {
  if (!settingKey) return null
  return PROTECTED_SETTINGS.find(setting => setting.key === settingKey) || null
}

/**
 * Get protected setting group info by key
 * Lấy thông tin nhóm cài đặt được bảo vệ theo key
 * @param {string} groupKey - The group key (group_key field)
 * @returns {object|null} - Protected group info or null
 */
export const getProtectedSettingGroupInfo = (groupKey) => {
  if (!groupKey) return null
  return PROTECTED_SETTING_GROUPS.find(group => group.group_key === groupKey) || null
}

/**
 * Get all protected settings in a specific group
 * Lấy tất cả cài đặt được bảo vệ trong nhóm cụ thể
 * @param {string} groupKey - The group key (group_key field)
 * @returns {array} - Array of protected settings in the group
 */
export const getProtectedSettingsInGroup = (groupKey) => {
  if (!groupKey) return []
  return PROTECTED_SETTINGS.filter(setting => setting.group_key === groupKey)
}

/**
 * Check if a setting group has any protected settings
 * Kiểm tra xem nhóm cài đặt có chứa cài đặt được bảo vệ nào không
 * @param {string} groupKey - The group key (group_key field)
 * @returns {boolean} - True if the group has protected settings
 */
export const hasProtectedSettingsInGroup = (groupKey) => {
  if (!groupKey) return false
  return PROTECTED_SETTINGS.some(setting => setting.group_key === groupKey)
}

/**
 * Get deletion error message for protected setting group
 * Lấy thông báo lỗi khi xóa nhóm cài đặt được bảo vệ
 * @param {string} groupKey - The group key (group_key field)
 * @returns {string} - Error message
 */
export const getProtectedGroupDeletionMessage = (groupKey) => {
  const groupInfo = getProtectedSettingGroupInfo(groupKey)
  if (groupInfo) {
    return `Không thể xóa nhóm cài đặt "${groupInfo.displayName}". ${groupInfo.reason || 'Đây là nhóm cài đặt quan trọng của hệ thống.'}`
  }
  return 'Không thể xóa nhóm cài đặt này vì nó được bảo vệ bởi hệ thống.'
}

/**
 * Get deletion error message for protected setting
 * Lấy thông báo lỗi khi xóa cài đặt được bảo vệ
 * @param {string} settingKey - The setting key
 * @returns {string} - Error message
 */
export const getProtectedSettingDeletionMessage = (settingKey) => {
  const settingInfo = getProtectedSettingInfo(settingKey)
  if (settingInfo) {
    return `Không thể xóa cài đặt "${settingInfo.displayName}". ${settingInfo.reason || 'Đây là cài đặt quan trọng của hệ thống.'}`
  }
  return 'Không thể xóa cài đặt này vì nó được bảo vệ bởi hệ thống.'
}


export const getProtectedGroupTooltipMessage = (groupKey) => {
  const groupInfo = getProtectedSettingGroupInfo(groupKey)
  if (groupInfo) {
    return groupInfo.reason || 'Đây là nhóm cài đặt quan trọng của hệ thống và không thể xóa'
  }
  return 'Nhóm cài đặt này được bảo vệ và không thể xóa'
}


export const getProtectedSettingTooltipMessage = (settingKey) => {
  const settingInfo = getProtectedSettingInfo(settingKey)
  if (settingInfo) {
    return settingInfo.reason || 'Đây là cài đặt quan trọng của hệ thống và không thể xóa'
  }
  return 'Cài đặt này được bảo vệ và không thể xóa'
}

export default {
  PROTECTED_SETTING_GROUPS,
  PROTECTED_SETTINGS,
  isProtectedSettingGroup,
  isProtectedSettingGroupByName,
  isProtectedSetting,
  isProtectedSettingInGroup,
  getProtectedSettingInfo,
  getProtectedSettingGroupInfo,
  getProtectedSettingsInGroup,
  hasProtectedSettingsInGroup,
  getProtectedGroupDeletionMessage,
  getProtectedSettingDeletionMessage,
  getProtectedGroupTooltipMessage,
  getProtectedSettingTooltipMessage
}
