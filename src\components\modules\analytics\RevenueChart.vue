<template>
  <div class="revenue-chart">
    <!-- Header với bộ lọc -->
    <div class="chart-header mb-6">
      <div class="flex flex-col md:flex-row md:justify-between md:items-center gap-4">
        <!-- <PERSON><PERSON>h thu tổng -->
        <div class="total-revenue">
          <div class="summary-item">
            <span class="summary-label"><PERSON><PERSON><PERSON> thu tổng:</span>
            <span class="summary-value text-green-600">{{ formatCurrency(totalRevenue) }}</span>
          </div>
        </div>

        <!-- Bộ lọc thời gian -->
        <div class="flex items-center gap-3 w-full md:w-auto">
          <!-- Date Range Picker với Quick Options -->
          <div class="flex items-center gap-2 w-full md:w-auto">
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              range-separator="đến"
              start-placeholder="Từ ngày"
              end-placeholder="Đến ngày"
              format="DD/MM/YYYY"
              value-format="YYYY-MM-DD"
              @change="handleDateRangeChange"
              @clear="handleDateRangeClear"
              size="small"
              class="date-picker-mobile"
              :shortcuts="dateShortcuts"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- Biểu đồ -->
    <div class="chart-container">
      <div v-if="loading" class="loading-container">
        <el-loading text="Đang tải dữ liệu..." />
      </div>
      <div ref="chartRef" class="chart-canvas" v-show="!loading"></div>

      <!-- Custom Tooltip -->
      <div
        v-if="customTooltip.show"
        class="custom-tooltip"
        :style="{
          left: customTooltip.x + 'px',
          top: customTooltip.y + 'px'
        }"
      >
        <div class="tooltip-content">
          <div class="tooltip-date">{{ customTooltip.date }}</div>
          <div class="tooltip-item">
            <span class="tooltip-color" style="background: #10b981;"></span>
            Doanh thu: <strong>{{ formatCurrency(customTooltip.revenue) }}</strong>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import * as echarts from 'echarts'
import { useStatistics } from '@/composables/modules/analytics/index.js'

// Props
const props = defineProps({
  height: {
    type: String,
    default: '400px'
  }
})

// Use statistics composable
const { 
  loading, 
  fetchRevenueStatistics, 
  formatDateForAPI, 
  formatRevenueChartDataFromAPI,
  getDateRangePresets 
} = useStatistics()

// Reactive data
const chartRef = ref(null)
const chartInstance = ref(null)
const dateRange = ref([])

// Chart data
const chartData = ref({
  dates: [],
  revenues: []
})

// Total revenue data
const totalRevenue = ref(0)

// Custom tooltip data
const customTooltip = ref({
  show: false,
  x: 0,
  y: 0,
  date: '',
  revenue: 0
})

// Date shortcuts for quick selection
const dateShortcuts = getDateRangePresets()

// Initialize date range (15 days by default)
const initializeDateRange = () => {
  const end = new Date()
  const start = new Date()
  start.setDate(start.getDate() - 15)
  
  dateRange.value = [
    start.toISOString().split('T')[0],
    end.toISOString().split('T')[0]
  ]
}

// Format currency helper
const formatCurrency = (amount) => {
  return new Intl.NumberFormat('vi-VN', {
    style: 'currency',
    currency: 'VND'
  }).format(amount || 0)
}

// Handle date range change
const handleDateRangeChange = () => {
  if (dateRange.value && dateRange.value.length === 2) {
    loadChartData()
  }
}

// Handle date range clear
const handleDateRangeClear = () => {
  // Reset to default 15 days when clearing
  initializeDateRange()
  loadChartData()
}

// Load chart data
const loadChartData = async () => {
  if (!dateRange.value || dateRange.value.length !== 2) return

  try {
    const [startDate, endDate] = dateRange.value

    // Call API to get revenue statistics
    const result = await fetchRevenueStatistics({
      start_date: formatDateForAPI(startDate),
      end_date: formatDateForAPI(endDate)
    })

    if (result.success && result.data) {
      // Process the new API response structure
      if (result?.data?.daily_revenue && result?.data?.total_revenue !== undefined) {
        const dates = []
        const revenues = []

        result?.data?.daily_revenue.forEach(item => {
          dates.push(item.date)
          revenues.push(item.revenue)
        })

        chartData.value = { dates, revenues }
        totalRevenue.value = result?.data?.total_revenue
      } else {
        // Fallback to old format if needed
        chartData.value = formatRevenueChartDataFromAPI(result?.data)
        totalRevenue.value = result?.data?.total_revenue || 0
      }
      updateChart()
    } else {
      // Set empty data on failure
      chartData.value = {
        dates: [],
        revenues: []
      }
      totalRevenue.value = 0
      updateChart()
      ElMessage.error(result.message || 'Lỗi khi tải dữ liệu biểu đồ doanh thu')
    }

  } catch (error) {
    console.error('Error loading revenue chart data:', error)
    // Set empty data on error
    chartData.value = {
      dates: [],
      revenues: []
    }
    totalRevenue.value = 0
    updateChart()
    ElMessage.error('Lỗi khi tải dữ liệu biểu đồ doanh thu')
  }
}

// Initialize chart
const initChart = () => {
  if (!chartRef.value) return
  
  chartInstance.value = echarts.init(chartRef.value)
  
  // Handle empty data case
  if (!chartData.value.dates.length) {
    const isMobile = window.innerWidth < 768
    const isSmallMobile = window.innerWidth < 480
    
    const option = {
      title: {
        text: isMobile ? 'Doanh thu' : 'Thống kê doanh thu',
        left: 'center',
        top: isMobile ? 5 : 10,
        textStyle: {
          fontSize: isSmallMobile ? 12 : isMobile ? 14 : 16,
          fontWeight: 'bold',
          fontFamily: 'GitLab Sans, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Noto Sans, sans-serif'
        }
      },
      graphic: {
        type: 'text',
        left: 'center',
        top: 'middle',
        style: {
          text: 'Không có dữ liệu để hiển thị',
          fontSize: isSmallMobile ? 12 : isMobile ? 14 : 16,
          fill: '#999',
          fontFamily: 'GitLab Sans, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Noto Sans, sans-serif'
        }
      }
    }
    chartInstance.value.setOption(option)
    return
  }
  
  // Calculate max value for proper scaling
  const maxRevenue = Math.max(...chartData.value.revenues, 0)
  const interval = maxRevenue > 0 ? Math.ceil(maxRevenue / 5) : 20
  const yAxisMax = interval * 5
  
  // Responsive configuration
  const isMobile = window.innerWidth < 768
  const isSmallMobile = window.innerWidth < 480
  
  const option = {
    title: {
      text: isMobile ? 'Doanh thu' : 'Thống kê doanh thu',
      left: 'center',
      top: isMobile ? 5 : 10,
      textStyle: {
        fontSize: isSmallMobile ? 12 : isMobile ? 14 : 16,
        fontWeight: 'bold',
        fontFamily: 'GitLab Sans, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Noto Sans, sans-serif'
      }
    },
    tooltip: {
      show: false
    },
    legend: {
      data: [isMobile ? 'DT' : 'Doanh thu'],
      top: isMobile ? 35 : 50,
      textStyle: {
        fontSize: isSmallMobile ? 10 : isMobile ? 11 : 12
      }
    },
    grid: {
      left: isMobile ? '10%' : '8%',
      right: isMobile ? '10%' : '8%',
      bottom: '3%',
      top: isMobile ? '25%' : '20%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: chartData.value.dates,
      axisLabel: {
        rotate: 0, // Đặt về 0 để hiển thị ngang
        fontSize: isSmallMobile ? 9 : isMobile ? 10 : 11,
        interval: isMobile ? 'auto' : 0,
        margin: isMobile ? 6 : 8
      }
    },
    yAxis: {
      type: 'value',
      name: isMobile ? 'VND' : 'Doanh thu (VND)',
      position: 'left',
      max: yAxisMax,
      interval: interval,
      nameTextStyle: {
        fontSize: isSmallMobile ? 9 : isMobile ? 10 : 12
      },
      axisLabel: {
        fontSize: isSmallMobile ? 9 : isMobile ? 10 : 11,
        formatter: function(value) {
          if (value >= 1000000) {
            return (value / 1000000).toFixed(1) + 'M'
          } else if (value >= 1000) {
            return (value / 1000).toFixed(1) + 'K'
          }
          return value.toString()
        }
      }
    },
    series: [
      {
        name: isMobile ? 'DT' : 'Doanh thu',
        type: 'line',
        data: chartData.value.revenues,
        smooth: false, // Thay đổi từ true thành false để có đường thẳng
        symbol: 'circle',
        symbolSize: isMobile ? 6 : 8,
        lineStyle: {
          width: isMobile ? 2 : 3,
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [{
              offset: 0, color: '#10b981' // green-500
            }, {
              offset: 1, color: '#059669' // green-600
            }]
          }
        },
        itemStyle: {
          color: '#10b981',
          borderColor: '#ffffff',
          borderWidth: isMobile ? 1 : 2
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [{
              offset: 0, color: 'rgba(16, 185, 129, 0.3)'
            }, {
              offset: 1, color: 'rgba(16, 185, 129, 0.05)'
            }]
          }
        }
      }
    ]
  }
  
  chartInstance.value.setOption(option)

  // Custom tooltip handlers - Sử dụng cách đơn giản hơn
  chartInstance.value.getZr().on('mousemove', function(event) {
    try {
      const mouseX = event.offsetX;
      const mouseY = event.offsetY;

      // Lấy kích thước chart
      const chartDom = chartInstance.value.getDom();
      const chartWidth = chartDom.clientWidth;
      const chartHeight = chartDom.clientHeight;

      // Ước tính vùng grid (dựa trên responsive config)
      const isMobile = window.innerWidth < 768;
      const leftPercent = isMobile ? 10 : 8;
      const rightPercent = isMobile ? 10 : 8;
      const topPercent = isMobile ? 25 : 20;
      const bottomPercent = 3;

      const gridLeft = chartWidth * (leftPercent / 100);
      const gridRight = chartWidth * (1 - rightPercent / 100);
      const gridTop = chartHeight * (topPercent / 100);
      const gridBottom = chartHeight * (1 - bottomPercent / 100);

      // Kiểm tra xem chuột có trong vùng grid không
      if (mouseX >= gridLeft && mouseX <= gridRight &&
          mouseY >= gridTop && mouseY <= gridBottom) {

        const dataLength = chartData.value.dates.length;
        if (dataLength > 0) {
          // Tính toán dataIndex dựa trên vị trí X
          const gridWidth = gridRight - gridLeft;
          const relativeX = mouseX - gridLeft;
          const dataIndex = Math.round((relativeX / gridWidth) * (dataLength - 1));

          // Đảm bảo dataIndex hợp lệ
          if (dataIndex >= 0 && dataIndex < dataLength) {
            const date = chartData.value.dates[dataIndex];
            const revenue = chartData.value.revenues[dataIndex] || 0;

            customTooltip.value = {
              show: true,
              x: mouseX + 10,
              y: mouseY - 10,
              date: date,
              revenue: revenue
            };
          }
        }
      } else {
        customTooltip.value.show = false;
      }
    } catch (error) {
      console.warn('Tooltip error:', error);
      customTooltip.value.show = false;
    }
  });

  chartInstance.value.getZr().on('mouseout', function() {
    customTooltip.value.show = false;
  });
}

// Update chart
const updateChart = () => {
  if (!chartInstance.value) return
  
  // Calculate max value for proper scaling
  const maxRevenue = Math.max(...chartData.value.revenues, 0)
  const interval = maxRevenue > 0 ? Math.ceil(maxRevenue / 5) : 20
  const yAxisMax = interval * 5
  
  const option = {
    xAxis: {
      data: chartData.value.dates
    },
    yAxis: {
      max: yAxisMax,
      interval: interval
    },
    series: [
      {
        data: chartData.value.revenues
      }
    ]
  }
  
  chartInstance.value.setOption(option)
}

// Handle window resize
const handleResize = () => {
  if (chartInstance.value && chartInstance.value.getDom()) {
    try {
      chartInstance.value.resize()
    } catch (error) {
      console.warn('Chart resize error:', error)
      // Re-initialize chart if resize fails
      setTimeout(() => {
        if (chartData.value.dates.length > 0) {
          initChart()
        }
      }, 100)
    }
  }
}

// Lifecycle
onMounted(() => {
  initializeDateRange()
  
  // Add resize listener
  window.addEventListener('resize', handleResize)
  
  // Load data after a short delay to ensure DOM is ready
  setTimeout(() => {
    loadChartData()
  }, 100)
})

onUnmounted(() => {
  // Remove resize listener first
  window.removeEventListener('resize', handleResize)
  
  // Dispose chart instance
  if (chartInstance.value) {
    try {
      chartInstance.value.dispose()
    } catch (error) {
      console.warn('Chart dispose error:', error)
    }
    chartInstance.value = null
  }
})

// Watch for chart data changes
watch(chartData, () => {
  if (chartInstance.value && chartInstance.value.getDom()) {
    updateChart()
  } else {
    // Initialize chart after data is ready
    setTimeout(() => {
      if (chartRef.value && chartData.value.dates.length > 0) {
        initChart()
      }
    }, 50)
  }
}, { deep: true })
</script>

<style lang="scss" scoped>
.revenue-chart {
  padding: 0.75rem;
  background: white;
  border-radius: 0.75rem;
  border: 1px solid #e5e7eb;
  
  // Mobile responsive
  @media (min-width: 768px) {
    padding: 1rem;
  }
  
  // Dark mode support
  .dark & {
    background-color: #1f2937;
    border-color: #374151;
  }
}

.chart-header {
  .total-revenue {
    h3 {
      .dark & {
        color: #f9fafb;
      }
    }
  }

  .text-gray-800 {
    .dark & {
      color: #f9fafb;
    }
  }

  .text-gray-500 {
    .dark & {
      color: #9ca3af;
    }
  }
}

.summary-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;

  @media (min-width: 640px) {
    flex-direction: row;
    align-items: center;
    gap: 0.5rem;
  }
}

.summary-label {
  font-size: 0.875rem;
  color: #6b7280;
  font-weight: 500;

  .dark & {
    color: #9ca3af;
  }
}

.summary-value {
  font-size: 1.125rem;
  font-weight: 700;

  @media (min-width: 640px) {
    font-size: 1.25rem;
  }
}

.chart-container {
  padding: 0 5px;
  position: relative;
  
  // Mobile responsive
  @media (min-width: 480px) {
    padding: 0 8px;
  }
  
  @media (min-width: 768px) {
    padding: 0 20px;
  }
  
  .loading-container {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.9);
    z-index: 10;
  }
  
  .chart-canvas {
    width: 100%;
    height: v-bind(height);
    min-height: 280px; // Reduced minimum height for mobile
    
    @media (min-width: 480px) {
      min-height: 300px;
    }
    
    @media (min-width: 768px) {
      min-height: 350px;
    }
  }
}

// El-date-picker styling with mobile responsive
.date-picker-mobile {
  width: 100% !important;
  min-width: 180px;
  max-width: 100%;
  
  @media (min-width: 480px) {
    min-width: 200px;
    max-width: 280px;
  }
  
  @media (min-width: 640px) {
    max-width: 280px;
  }
  
  @media (min-width: 768px) {
    width: 240px !important;
    max-width: 240px;
  }
}

:deep(.el-date-editor) {
  width: 100% !important;
  max-width: 100% !important;
  
  .el-input__wrapper {
    width: 100% !important;
    border-radius: 6px;
    border: 1px solid #d1d5db;
    
    &:hover {
      border-color: #9ca3af;
    }
    
    &.is-focus {
      border-color: #3b82f6;
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }
  }
  
  // Mobile responsive text size
  .el-input__inner {
    font-size: 13px;
    width: 100% !important;
    
    @media (max-width: 479px) {
      font-size: 11px;
    }
    
    @media (max-width: 767px) {
      font-size: 12px;
    }
  }
  
  // Prevent overflow on mobile
  .el-range-separator {
    @media (max-width: 479px) {
      font-size: 10px;
      padding: 0 1px;
    }
    
    @media (max-width: 640px) {
      font-size: 11px;
      padding: 0 2px;
    }
  }
  
  .el-range-input {
    @media (max-width: 479px) {
      font-size: 11px;
    }
    
    @media (max-width: 640px) {
      font-size: 12px;
    }
  }
}

// Mobile responsive shortcuts popover
:deep(.el-picker-panel__shortcut) {
  @media (max-width: 479px) {
    font-size: 11px;
    padding: 6px 8px;
  }
  
  @media (max-width: 767px) {
    font-size: 12px;
    padding: 8px 12px;
  }
}

// Additional mobile responsiveness
:deep(.el-popper) {
  @media (max-width: 479px) {
    max-width: 90vw !important;
  }
}

// Custom tooltip styles
.custom-tooltip {
  position: absolute;
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid #ccc;
  border-radius: 4px;
  padding: 10px 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  z-index: 9999;
  pointer-events: none;
  font-size: 14px;
  min-width: 160px;
}

.tooltip-content {
  .tooltip-date {
    font-weight: bold;
    margin-bottom: 8px;
    color: #333;
  }

  .tooltip-item {
    margin: 4px 0;
    display: flex;
    align-items: center;
    color: #333;

    .tooltip-color {
      display: inline-block;
      width: 12px;
      height: 12px;
      margin-right: 8px;
      border-radius: 2px;
      flex-shrink: 0;
    }
  }
}

// Tooltip responsive styling
:deep(.echarts-tooltip) {
  @media (max-width: 479px) {
    max-width: 180px !important;
    font-size: 11px !important;
  }

  @media (max-width: 767px) {
    max-width: 220px !important;
    font-size: 12px !important;
  }
}
</style>
